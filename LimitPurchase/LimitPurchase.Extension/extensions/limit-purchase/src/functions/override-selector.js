import { SELECTOR_ELEMENT } from "../main";
import { parameter } from "../utils/parameter";

const SELECTOR_CONFIG = [
  {
    key: "QUANTITY_INPUT",
    append: "All_QUANTITY_INPUT_APPEND",
    override: "All_QUANTITY_INPUT_OVR"
  },
  {
    key: "ORICHI_BUTTON_IN_FORM_CART",
    append: "All_BUTTON_IN_FORM_CART_APPEND",
    override: "All_BUTTON_IN_FORM_CART_OVR"
  },
  {
    key: "ORICHI_VARIANT_SELECTOR",
    append: "All_VARIANT_SELECTOR_APPEND",
    override: "All_VARIANT_SELECTOR_OVR"
  },
  {
    key: "ORICHI_BUTTON_CHECKOUT",
    append: "All_BUTTON_CHECKOUT_APPEND",
    override: "All_BUTTON_CHECKOUT_OVR"
  },
  {
    key: "ORICHI_BUTTON_PLUS_MINUS",
    append: "All_BUTTON_PLUS_MINUS_APPEND",
    override: "All_BUTTON_PLUS_MINUS_OVR"
  },
  {
    key: "CART_REMOVE_BUTTON",
    append: "All_REMOVE_BUTTON_APPEND",
    override: "All_REMOVE_BUTTON_OVR"
  },
  {
    key: "ORICHI_CART_POPUP_SELECTOR",
    append: "All_CART_POPUP_SELECTOR_APPEND",
    override: "All_CART_POPUP_SELECTOR_OVR"
  },
  {
    key: "ORICHI_DISABLE_BUTTON_APP_IF_LIMITED",
    append: "All_DISABLE_BUTTON_APP_IF_LIMITED_APPEND",
    override: "All_DISABLE_BUTTON_APP_IF_LIMITED_OVR"
  },
  {
    key: "ORICHI_BUTTON_DYNAMIC_CHECKOUT",
    append: "All_BUTTON_DYNAMIC_CHECKOUT_APPEND",
    override: "All_BUTTON_DYNAMIC_CHECKOUT_OVR"
  }
];

export const overrideSelector = () => {
  const { getValue } = parameter();

  SELECTOR_CONFIG.forEach(({ key, append, override }) => {
    const appendValue = getValue(append);
    const overrideValue = getValue(override);

    if (appendValue) {
      SELECTOR_ELEMENT.appendSelector(key, appendValue);
    }
    if (overrideValue) {
      SELECTOR_ELEMENT.setSelector(key, overrideValue);
    }
  });
};
