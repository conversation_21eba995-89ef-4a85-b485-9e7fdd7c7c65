import { TargetEnum } from "../enums/target";
import { ValidationRuleEnum } from "../enums/validation-rule";
import { parameter } from "../utils/parameter";
import { findProductInLimit } from "./find-product";

/**
 * Sets min and max attributes for quantity inputs in the cart based on validation rules
 * @param {Object} cartItems - The cart items object
 */
export const setMinMaxForQuantityInputs = (cartItems) => {
  const cartPage = __st.p === "cart";

  // Process each item in the cart
  for (const item of cartItems.ListItems) {
    const { product_id, variant_id } = item;

    // Find applicable limit for this item
    const currentLimit = findApplicableLimit(product_id, variant_id, cartPage);
    if (!currentLimit) continue;

    // Get handles or IDs from ShopifyObjects
    const handles = getHandlesOrIds(currentLimit);
    if (!handles.length) continue;

    // Find the quantity input element for this item
    const quantityInput = findQuantityInputForItem(handles);
    if (!quantityInput) continue;

    // Apply validation rules to the quantity input
    applyValidationRulesToInput(quantityInput, currentLimit);
  }
};

/**
 * Finds the applicable product or variant limit for a cart item
 * @param {string|number} productId - The product ID
 * @param {string|number} variantId - The variant ID
 * @param {boolean} cartPage - Whether we're on the cart page
 * @returns {Object|null} The applicable limit object or null if none found
 */
const findApplicableLimit = (productId, variantId, cartPage) => {
  const targetType = cartPage ? TargetEnum.PRODUCT_TAG : undefined;

  // Try to find limits for both product and variant
  const productLimit = findProductInLimit(productId, targetType);
  const variantLimit = findProductInLimit(variantId, targetType);

  // Return the first applicable limit found
  return productLimit || variantLimit;
};

/**
 * Extracts handles or IDs from a limit's ShopifyObjects
 * @param {Object} limit - The limit object
 * @returns {string[]} Array of handles or IDs
 */
const getHandlesOrIds = (limit) => {
  // Validate ShopifyObjects exists and is not empty
  if (!limit.ShopifyObjects || limit.ShopifyObjects.length === 0) {
    return [];
  }

  // Check if the target is valid for our purposes
  const validTargets = [TargetEnum.VARIANT, TargetEnum.PRODUCT];
  if (!validTargets.includes(limit.Target)) {
    return [];
  }

  // Extract handles or IDs based on the target type
  if (limit.Target === TargetEnum.VARIANT) {
    return limit.ShopifyObjects.filter((obj) => !!obj?.Id && obj.Id.toString().trim() !== "").map(
      (obj) => obj.Id.toString()
    );
  } else {
    return limit.ShopifyObjects.filter((obj) => !!obj?.Handle && obj.Handle.trim() !== "").map(
      (obj) => obj.Handle
    );
  }
};

/**
 * Finds the quantity input element for a cart item using its handles
 * @param {string[]} handles - Array of product handles or variant IDs
 * @returns {HTMLElement|null} The quantity input element or null if not found
 */
const findQuantityInputForItem = (handles) => {
  const { getValue } = parameter();

  // Get all product links in the cart
  const productLinksSelector =
    getValue("CART_PRODUCT_LINKS") || "a.cart-item__name, a.cart-item__link";
  const productLinks = Array.from(document.querySelectorAll(productLinksSelector));

  // Find the first link that matches any of our handles
  let targetLink = null;
  for (const handle of handles) {
    targetLink = productLinks.find((link) => link.href.includes(handle));
    if (targetLink) break;
  }

  if (!targetLink) return null;

  // Find the container row for this product
  const rowSelector =
    getValue("CART_CLOSEST_PRODUCT_LINKS") || ".drawer__cart-items-wrapper, .cart-item, .cart__row";
  const row = targetLink.closest(rowSelector);
  if (!row) return null;

  // Find the quantity input within this row
  const inputSelector =
    getValue("CART_QUANTITY_INPUT") ||
    '.quantity__input, [name="updates[]"], .cart__qty-input, input[type="number"]';
  return row.querySelector(inputSelector);
};

/**
 * Applies validation rules to a quantity input element
 * @param {HTMLElement} input - The quantity input element
 * @param {Object} limit - The limit object containing validation rules
 */
const applyValidationRulesToInput = (input, limit) => {
  const { getValue } = parameter();

  // Find min and max rules
  const minRule = limit.ValidationRules.find(
    (rule) => rule.Type === ValidationRuleEnum.MINIMUM_QUANTITY
  );

  const maxRule = limit.ValidationRules.find(
    (rule) => rule.Type === ValidationRuleEnum.MAXIMUM_QUANTITY
  );

  // Apply min attribute if enabled
  if (minRule && getValue("CART_SET_MIN_QUANTITY_INPUTS")) {
    input.setAttribute("min", minRule.Value);
  }

  // Apply max attribute if enabled
  if (maxRule && getValue("CART_SET_MAX_QUANTITY_INPUTS")) {
    input.setAttribute("max", maxRule.Value);
  }

  // Mark this input as processed
  input.classList.add("orichi-limit-input-cart");
};
