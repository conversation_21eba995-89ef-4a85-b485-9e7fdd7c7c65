import { overrideApi } from "../apis/override.api";
import {
  firstRender,
  ValidationProduct,
  ValidationVariant
} from "../components/validation-product";
import { findProductInLimit } from "../functions/find-product";
import { SELECTOR_ELEMENT } from "../main";
import { removeDuplicateErrors, wait } from "../utils/common";
import { parameter } from "../utils/parameter";

export const ProductPage = async () => {
  const { getValue } = parameter();

  if (__st.p !== "product" || getValue("PRODUCT_PAGE_LIMIT_NO_CHECK") === "true") return;

  if (!window?.orichiLimit?.currentProduct?.available) return;

  let variantElement = $(SELECTOR_ELEMENT.ORICHI_VARIANT_SELECTOR);

  const currentProduct = window.orichiLimit.currentProduct;
  const currentLimitProduct = findProductInLimit(currentProduct.id, currentProduct);
  const currentVariant = $(SELECTOR_ELEMENT.ORICHI_VARIANT_SELECTOR).val();
  const currentLimitVariant = findProductInLimit(currentVariant, currentProduct);

  await initializeLimit(currentLimitProduct, currentLimitVariant);

  const handleVariantChange = async () => {
    const newVariant = variantElement.val();
    const updatedLimitVariant = findProductInLimit(newVariant);
    await initializeLimit(currentLimitProduct, updatedLimitVariant);

    if (
      Shopify.shop === "pu-ding-ding-dev.myshopify.com" ||
      Shopify.shop === "super-magnet-man-2.myshopify.com"
    ) {
      $(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).attr("disabled", false);
    }
  };

  const VARIANT_CHANGE_DEBOUNCE_DELAY = 300;

  let previousVariantValue = variantElement.val();
  let debounceTimer = null;

  const processVariantChange = (newVariantValue) => {
    if (newVariantValue !== previousVariantValue) {
      previousVariantValue = newVariantValue;
      handleVariantChange();
    }
  };

  const onVariantChange = () => {
    const currentVariantValue = variantElement.val();

    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }

    if (currentVariantValue !== previousVariantValue) {
      processVariantChange(currentVariantValue);
    } else {
      if (Shopify.shop === "7c7392-a8.myshopify.com") {
        debounceTimer = setTimeout(() => {
          const debouncedVariantValue = variantElement.val();
          processVariantChange(debouncedVariantValue);
          debounceTimer = null;
        }, VARIANT_CHANGE_DEBOUNCE_DELAY);
        const findVariantInRule = currentLimitVariant?.ShopifyObjects.find(
          (p) => p.Id == currentVariantValue
        );
        if (!findVariantInRule) {
          $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(1);
        } else {
          firstRender(currentLimitVariant);
        }
      }
    }
  };

  variantElement.on("change", onVariantChange);

  $(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).on("click", async (event) => {
    let cartElement = $(SELECTOR_ELEMENT.ORICHI_BUTTON_IN_FORM_CART);
    cartElement.not(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).attr("disabled", true);
    const newVariant = variantElement.val();
    const currentProduct = window.orichiLimit.currentProduct;
    const updatedLimitVariant = findProductInLimit(currentProduct.id || newVariant);

    if (!updatedLimitVariant) {
      cartElement.not(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).attr("disabled", false);
      return;
    }

    overrideApi(async () => {
      await handleFormSubmit(currentLimitProduct, updatedLimitVariant);
    });
  });
};

const initializeLimit = async (productLimitItem, variantLimitItem) => {
  let cartElement = $(SELECTOR_ELEMENT.ORICHI_BUTTON_IN_FORM_CART);
  let variantElement = $(SELECTOR_ELEMENT.ORICHI_VARIANT_SELECTOR);
  const { getValue } = parameter();

  cartElement.not(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).attr("disabled", true);
  await wait(500);

  firstRender(productLimitItem || variantLimitItem);
  let errors = [];

  if (productLimitItem) {
    let currentProduct = productLimitItem.ShopifyObjects.find(
      (p) => p.Id == window.orichiLimit.currentProduct.id
    );
    if (
      currentProduct &&
      productLimitItem.ValidationRules &&
      productLimitItem.AlertProductPageMessage != null
    ) {
      productLimitItem.AlertProductPageMessage =
        productLimitItem.AlertProductPageMessage.replaceAll(
          "{product_name}",
          currentProduct.Title
        ).replaceAll("{quantity}", productLimitItem.ValidationRules[0].Value);
    }
    const { errors: productErrors, currentLimit: productLimit } =
      await ValidationProduct(productLimitItem);
    errors = [...errors, ...productErrors];
  }

  if (variantLimitItem) {
    let currentVariant = variantLimitItem.ShopifyObjects.find(
      (p) => p.Id == $(SELECTOR_ELEMENT.ORICHI_VARIANT_SELECTOR).val()
    );
    if (
      currentVariant &&
      variantLimitItem.ValidationRules &&
      variantLimitItem.AlertProductPageMessage != null
    ) {
      variantLimitItem.AlertProductPageMessage =
        variantLimitItem.AlertProductPageMessage.replaceAll(
          "{product_name}",
          currentVariant.ParentName
        )
          .replaceAll("{variant_name}", currentVariant.Title)
          .replaceAll("{quantity}", variantLimitItem.ValidationRules[0].Value);
    }
    const { errors: variantErrors, currentLimit: variantLimit } =
      await ValidationVariant(variantLimitItem);
    errors = [...errors, ...variantErrors];
  }

  // Remove duplicate errors
  errors = removeDuplicateErrors(errors);

  $(SELECTOR_ELEMENT.QUANTITY_INPUT)
    .off("change")
    .on("change", async () => {
      let newErrors = [];
      if (productLimitItem) {
        const { errors: productErrors } = await ValidationProduct(productLimitItem);
        newErrors = [...newErrors, ...productErrors];
      }
      if (variantLimitItem) {
        const { errors: variantErrors } = await ValidationVariant(variantLimitItem);
        newErrors = [...newErrors, ...variantErrors];
      }
      errors = removeDuplicateErrors(newErrors);
      updateErrorDisplay(errors, productLimitItem || variantLimitItem);
    });

  //Off event de tranh call voi variantLimitItem cu cua lan init trc
  $(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS)
    .off("click")
    .on("click", async (e) => {
      if (Shopify.shop === "wegouda.myshopify.com") {
        e.preventDefault();
        e.stopPropagation();
      }
      let newErrors = [];
      let step = 0;

      const storesWithStepIncrement = [
        "super-magnet-man-2.myshopify.com",
        "petandgarden.myshopify.com",
        "aspireinnovate.myshopify.com"
      ];
      const incrementButtonClass = getValue("PRODUCT_CLASS_BUTTON_INCREMENT_STEP");

      if (storesWithStepIncrement.includes(Shopify.shop) || incrementButtonClass) {
        const isIncrementButton =
          $(e.target).is(
            `.vela-qty__adjust--plus,.wpbingo-qty__adjust--plus,.js-qty__adjust--plus,${
              incrementButtonClass || ""
            }`
          ) || $(e.target).attr("data-action") === "increase-picker-quantity";

        step = isIncrementButton ? 1 : -1;
      }
      if (productLimitItem) {
        const { errors: productErrors } = await ValidationProduct(productLimitItem, step);
        newErrors = [...newErrors, ...productErrors];
      }
      if (variantLimitItem) {
        const { errors: variantErrors } = await ValidationVariant(variantLimitItem, step);
        newErrors = [...newErrors, ...variantErrors];
      }
      errors = removeDuplicateErrors(newErrors);
      updateErrorDisplay(errors, productLimitItem || variantLimitItem);

      if (
        Shopify.shop === "pu-ding-ding-dev.myshopify.com" ||
        Shopify.shop === "super-magnet-man-2.myshopify.com"
      ) {
        let currentValue = parseInt($(SELECTOR_ELEMENT.QUANTITY_INPUT).val()) + step;
        handleQuantityLimits(variantLimitItem, currentValue);
        handleQuantityLimits(productLimitItem, currentValue);
      }
    });

  updateErrorDisplay(errors, productLimitItem || variantLimitItem);

  if (Shopify.shop === "super-magnet-man-2.myshopify.com") {
    let currentValue = $(SELECTOR_ELEMENT.QUANTITY_INPUT).val();
    handleQuantityLimits(variantLimitItem, currentValue);
    handleQuantityLimits(productLimitItem, currentValue);
  }
};

const handleQuantityLimits = (limitItem, currentValue) => {
  if (!limitItem || !limitItem.ValidationRules || !limitItem.ValidationRules.length) return;

  for (let i = 0; i < limitItem.ValidationRules.length; i++) {
    let element = limitItem.ValidationRules[i];
    switch (element.Type) {
      case "maximum_quantity":
        if (currentValue == element.Value) {
          $("button.js-qty__adjust--plus, [data-action='increase-picker-quantity']").attr(
            "disabled",
            true
          );
        } else {
          $("button.js-qty__adjust--plus, [data-action='increase-picker-quantity']").attr(
            "disabled",
            false
          );
        }
        break;
      case "minimum_quantity":
        if (currentValue == element.Value) {
          $("button.js-qty__adjust--minus, [data-action='decrease-picker-quantity']").attr(
            "disabled",
            true
          );
        } else {
          $("button.js-qty__adjust--minus, [data-action='decrease-picker-quantity']").attr(
            "disabled",
            false
          );
        }
        break;

      default:
        break;
    }
  }
};

const updateErrorDisplay = (errors, currentLimit) => {
  const quantityInputElement = $(SELECTOR_ELEMENT.QUANTITY_INPUT);
  const cartElement = $(SELECTOR_ELEMENT.ORICHI_BUTTON_IN_FORM_CART);

  const { getValue } = parameter();
  const targetErrorMessageContainer = getValue("Product-Message-Error-Append-To");

  if (errors.length > 0 && currentLimit.AlertInProductPage) {
    const errorElement = `<div class="orichi-limit-error">${currentLimit.AlertProductPageMessage}</div>`;

    if ($(".orichi-limit-error").length === 0) {
      if (targetErrorMessageContainer) {
        $(targetErrorMessageContainer).append(errorElement);
        return;
      }

      if (quantityInputElement.length) {
        quantityInputElement.parent().parent().append(errorElement);
      } else {
        if (cartElement?.length === 1 || !cartElement.length) {
          $(errorElement).insertBefore(cartElement);
        } else {
          $(errorElement).insertBefore(cartElement[0]);
        }
      }
    } else {
      $(".orichi-limit-error").html(currentLimit.AlertProductPageMessage);
    }
    cartElement.not(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).attr("disabled", true);
  } else {
    $(".orichi-limit-error").remove();
    cartElement.attr("disabled", false);
  }
};

// New function to handle form submission
const handleFormSubmit = async (productLimitItem, variantLimitItem) => {
  let errors = [];
  if (productLimitItem) {
    const { errors: productErrors } = await ValidationProduct(productLimitItem);
    errors = [...errors, ...productErrors];
  }
  if (variantLimitItem) {
    const { errors: variantErrors } = await ValidationVariant(variantLimitItem);
    errors = [...errors, ...variantErrors];
  }

  errors = removeDuplicateErrors(errors);
  updateErrorDisplay(errors, productLimitItem || variantLimitItem);
};
