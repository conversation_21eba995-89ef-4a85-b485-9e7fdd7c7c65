import { orichiLimitGetVariants } from "../apis/get-variant.api";
import { overrideApi } from "../apis/override.api";
import { validateCartItems } from "../components/cart/validate-cart";
import { SELECTOR_ELEMENT } from "../main";
import { parameter } from "../utils/parameter";

export const CartPage = async () => {
  const { getValue } = parameter();
  if (__st.p !== "cart" || getValue("CART_PAGE_LIMIT_NO_CHECK") === "true") return;

  console.log("cart page");
  await validateCartItems();

  $(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS).on("click", async () => {
    $(SELECTOR_ELEMENT.ORICHI_BUTTON_CHECKOUT)
      .not(
        ".orichi-limit-modal-error",
        SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS,
        SELECTOR_ELEMENT.CART_REMOVE_BUTTON
      )
      .attr("disabled", true);

    overrideApi(async () => {
      const cartItems = await orichiLimitGetVariants();
      window.orichiLimit.cartItems = cartItems;
      await validateCartItems();
    });
  });

  $(SELECTOR_ELEMENT.CART_REMOVE_BUTTON).on("click", async () => {
    $(SELECTOR_ELEMENT.ORICHI_BUTTON_CHECKOUT)
      .not(
        ".orichi-limit-modal-error",
        SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS,
        SELECTOR_ELEMENT.CART_REMOVE_BUTTON
      )
      .attr("disabled", true);

    overrideApi(async () => {
      const cartItems = await orichiLimitGetVariants();
      window.orichiLimit.cartItems = cartItems;
      await validateCartItems();
    });
  });
};
