import { orichiLimitGetVariants } from "./apis/get-variant.api";
import { initParameter } from "./functions/init-parameter";
import { overrideSelector } from "./functions/override-selector";
import { CartPage } from "./pages/cart";
import { CartPopup } from "./pages/cart-popup";
import { ProductPage } from "./pages/product";

export const Run = async () => {
  console.log(
    "%c Madgic Order Limit js is working. Version 1.1.73",
    "background: #FFFF00; color: #000000; padding: 5px 10px; border-radius: 4px;"
  );

  const cartItems = await orichiLimitGetVariants();
  window.orichiLimit.cartItems = cartItems;

  overrideSelector();
  initParameter();

  CartPopup();
  ProductPage();
  CartPage();

  if (Shopify.shop === "98a125-a7.myshopify.com") {
    if (window.orichiLimit && window.orichiLimit.limits) {
      setInterval(() => {
        if ($(".orichi-limit-input-cart").length == 0) {
          window.orichiLimit.limits.forEach((element) => {
            let maxQuantity = element.ValidationRules.find((p) => p.Type == "maximum_quantity");
            let minQuantity = element.ValidationRules.find((p) => p.Type == "minimum_quantity");
            element.ShopifyObjects.forEach((e) => {
              if (maxQuantity) {
                $(".hdt-mini-cart__item:has(a[href*=" + e.Handle + "]) .hdt-quantity-input").attr(
                  "max",
                  maxQuantity.Value
                );
                $(
                  ".hdt-mini-cart__item:has(a[href*=" + e.Handle + "]) .hdt-quantity-input"
                ).addClass("orichi-limit-input-cart");
              }
              if (minQuantity) {
                $(".hdt-mini-cart__item:has(a[href*=" + e.Handle + "]) .hdt-quantity-input").attr(
                  "min",
                  minQuantity.Value
                );
                $(
                  ".hdt-mini-cart__item:has(a[href*=" + e.Handle + "]) .hdt-quantity-input"
                ).addClass("orichi-limit-input-cart");
              }
            });
          });
        }
      }, 500);
    }
  }
};
