import { Validation } from "../../common/validation";
import { TargetEnum } from "../../enums/target";
import { findProductInLimit } from "../../functions/find-product";
import { setMinMaxForQuantityInputs } from "../../functions/set-min-max-quantity-in-cart";
import { removeDuplicateErrors } from "../../utils/common";
import { parameter } from "../../utils/parameter";
import { updateErrorDisplay } from "./modal-error";

export const validateCartItems = async (isFirstRender = false) => {
  const cartItems = window.orichiLimit.cartItems;

  const { totalProductQuantities, totalVariantQuantities } = calculateTotalQuantities(cartItems);
  const allErrors = await validateAllItems(
    cartItems,
    totalProductQuantities,
    totalVariantQuantities
  );

  updateErrorDisplay(allErrors, isFirstRender);

  // Apply min/max values to quantity inputs if the parameter is enabled
  const { getValue } = parameter();
  if (getValue("CART_QUANTITY_INPUTS_SET_MIN_MAX")) {
    setMinMaxForQuantityInputs(cartItems);
  }
};

const calculateTotalQuantities = (cartItems) => {
  const totalProductQuantities = {};
  const totalVariantQuantities = {};

  for (const item of cartItems.ListItems) {
    const { product_id, variant_id, quantity } = item;
    totalProductQuantities[product_id] = (totalProductQuantities[product_id] || 0) + quantity;
    totalVariantQuantities[variant_id] = (totalVariantQuantities[variant_id] || 0) + quantity;
  }

  return { totalProductQuantities, totalVariantQuantities };
};

const validateAllItems = async (cartItems, totalProductQuantities, totalVariantQuantities) => {
  const allErrors = [];

  for (const item of cartItems.ListItems) {
    const { product_id, variant_id, product_title, variant_title } = item;
    const itemErrors = await validateItem(
      product_id,
      variant_id,
      product_title,
      variant_title,
      totalProductQuantities,
      totalVariantQuantities
    );
    allErrors.push(...itemErrors);
  }

  return removeDuplicateErrors(allErrors);
};

const validateItem = async (
  productId,
  variantId,
  product_title,
  variant_title,
  totalProductQuantities,
  totalVariantQuantities
) => {
  const cartPage = __st.p === "cart";
  const itemErrors = [];
  const totalQuantityProduct = Object.values(totalProductQuantities).reduce(
    (acc, curr) => acc + curr,
    0
  );

  const productLimit = findProductInLimit(productId, cartPage ? TargetEnum.PRODUCT_TAG : undefined);
  if (productLimit) {
    const { errors: productErrors } = await Validation({
      currentLimit: productLimit,
      totalQuantity: totalProductQuantities[productId]
    });

    productErrors.forEach((e) => {
      e.messageCheckoutPage = e.messageCheckoutPage
        .replaceAll("{product_name}", product_title)
        .replaceAll("{variant_name}", variant_title)
        .replaceAll("{quantity}", e.value);
    });

    itemErrors.push(...productErrors);
  }

  const variantLimit = findProductInLimit(variantId, cartPage ? TargetEnum.PRODUCT_TAG : undefined);
  if (variantLimit) {
    const { errors: variantErrors } = await Validation({
      currentLimit: variantLimit,
      totalQuantity: totalVariantQuantities[variantId]
    });

    variantErrors.forEach((e) => {
      e.messageCheckoutPage = e.messageCheckoutPage
        .replaceAll("{product_name}", product_title)
        .replaceAll("{variant_name}", variant_title)
        .replaceAll("{quantity}", e.value);
    });

    itemErrors.push(...variantErrors);
  }

  const targetOrder = window.orichiLimit.limits.find(
    (limit) => limit.Status && limit.Target === TargetEnum.CART
  );
  if (targetOrder) {
    const { errors: orderErrors } = await Validation({
      currentLimit: targetOrder,
      totalQuantity: totalQuantityProduct
    });
    itemErrors.push(...orderErrors);
  }

  return itemErrors;
};
