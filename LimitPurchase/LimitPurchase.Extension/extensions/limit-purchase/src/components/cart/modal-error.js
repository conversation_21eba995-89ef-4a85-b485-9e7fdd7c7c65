import { ModalError } from "../../common/modal-error";
import { TEXT } from "../../constants/text.constant";
import { SELECTOR_ELEMENT } from "../../main";
import { parameter } from "../../utils/parameter";

let buttonCheckoutInterval;

const handleButtonCheckout = (errors) => {
  const buttonCheckout = $(SELECTOR_ELEMENT.ORICHI_BUTTON_CHECKOUT);
  const buttonDynamicCheckout = $(SELECTOR_ELEMENT.ORICHI_BUTTON_DYNAMIC_CHECKOUT);
  const existingCloneElement = $(".orichi-limit-modal-error");
  const { getValue } = parameter();

  const buttonTextOrderIsNotValid = getValue("CART_BUTTON_CHECKOUT_TEXT_ORDER_IS_NOT_VALID");

  if (errors.length > 0 && buttonCheckout.length && !existingCloneElement.length) {
    $("<button>", {
      text: buttonTextOrderIsNotValid || TEXT.ORDER_INVALID,
      class: buttonCheckout.attr("class") + " orichi-limit-modal-error",
      type: "button",
      click: function () {
        $("#error-modal").css("display", "flex");
        $("body").css("overflow", "hidden");
      }
    }).insertAfter(buttonCheckout.hide());
    buttonDynamicCheckout.hide();
  }
};

export const updateErrorDisplay = (errors, isFirstRender = false) => {
  const { getValue } = parameter();

  const shouldCheckButtonInterval = getValue("CART_BUTTON_CHECKOUT_CHECK_INTERVAL");
  const checkButtonDelay = getValue("CART_BUTTON_CHECKOUT_DELAY_CHECK_INTERVAL");
  const showPopupOnlyOnCheckout = getValue("CART_SHOW_POPUP_WHEN_CLICK_BUTTON_CHECKOUT");

  // Clear existing interval if any
  if (buttonCheckoutInterval) {
    clearInterval(buttonCheckoutInterval);
  }

  if (errors.length > 0) {
    const existingModal = $("#error-modal");

    if (!existingModal.length) {
      ModalError(errors);

      // Add close modal event handler
      $("#close-modal").on("click", function () {
        $("#error-modal").css("display", "none");
        $("body").css("overflow", "auto");
      });
    } else {
      // Update existing modal content
      existingModal
        .find(".list-error")
        .html(errors.map((error) => `<li>${error.messageCheckoutPage}</li>`).join(""));
    }

    if (!isFirstRender) {
      if (showPopupOnlyOnCheckout) return;
      $("#error-modal").css("display", "flex");
      $("body").css("overflow", "hidden");
    }

    // Set up interval to check for button re-renders
    if (shouldCheckButtonInterval) {
      buttonCheckoutInterval = setInterval(() => {
        handleButtonCheckout(errors);
      }, checkButtonDelay || 500);
      return;
    }
    handleButtonCheckout(errors); // Initial call
  } else {
    // Clear interval when there are no errors
    if (buttonCheckoutInterval) {
      clearInterval(buttonCheckoutInterval);
    }

    // Remove modal if it exists
    const existingModal = $("#error-modal");
    existingModal.remove();

    // Restore checkout button and enable it
    const existingCloneElement = $(".orichi-limit-modal-error");
    const buttonCheckout = $(SELECTOR_ELEMENT.ORICHI_BUTTON_CHECKOUT);
    const buttonDynamicCheckout = $(SELECTOR_ELEMENT.ORICHI_BUTTON_DYNAMIC_CHECKOUT);
    if (existingCloneElement.length) {
      existingCloneElement.remove();
      buttonCheckout.show();
    }
    buttonCheckout.attr("disabled", false);
    buttonDynamicCheckout.show();
  }
};
