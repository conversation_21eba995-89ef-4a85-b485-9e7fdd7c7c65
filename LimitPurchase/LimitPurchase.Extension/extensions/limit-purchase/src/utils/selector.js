export const SELECTOR_ELEMENT_CONSTANT = {
  QUANTITY_INPUT: "[name='quantity']:first",
  ORICHI_BUTTON_IN_FORM_CART:
    "form[action='/cart/add'] button, form[action='/cart/add'] input[type='submit'], form[action*='/cart/add']:first button",
  ORICHI_VARIANT_SELECTOR: "[name='id']",
  ORICHI_BUTTON_CHECKOUT:
    '.juice-cart__totals__checkout, .cart__checkout-button, .js-cart-btn-checkout,#wof_checkout_btn,.button-checkout,.icart-checkout-btn,.rebuy-cart__checkout-button, .cart-button-checkout,form[action="/checkout"] button[type="submit"],[name="checkout"],.cart__checkout,.shopify-payment-button__button .cart__checkout,form[action^="/checkout"] button[name^="checkout"],form[action^="/checkout"] .checkout-button,form[action^="/cart"] button[name^="checkout"],form[action^="/cart"] .checkout-button, form[action^="/checkout"] button[name^="update"], form[action^="/checkout"] button[name^="checkout"], a[href="/checkout"], input[name="checkout"], .checkout, button[name="checkout"], #checkout2,#checkout,.Cart__Checkout, .Orichi-checkout-button, .Orichi-dynamic-checkout-button, .Orichi-dynamic-checkout-button2, .cart__checkout, .cart__submit, .orichiCheckout,.cart-drawer__checkout,.cart__checkout,.btn-checkout,#slidecarthq .footer-sticky .footer-buttons button,.cart_button_secure, form[action^="/checkout"] input[value^="Checkout"], .btn-checkout, .actions .checkout-cart,.site-header-cart__dropdown-cart-footer-btn--checkout, #shopify-section-gocart .l-cart__purchase-button, #CartDrawer-Checkout, button.checkout__button, button#CartDrawer-Checkout',
  ORICHI_BUTTON_PLUS_MINUS:
    "form[action='/cart/add'] .minus,form[action='/cart/add'] .plus,form[action='/cart/add'] .wpbQtyAdjustMinus,form[action='/cart/add'] .wpbQtyAdjustPlus,form[action='/cart/add'] .qtyplus,form[action='/cart/add'] .qtyminus, button[data-action='decrease-quantity'], button[data-action='increase-quantity'], button[name='minus'], button[name='plus'], .js-qty__adjust--minus, .js-qty__adjust--plus, .vela-qty__adjust--plus, .vela-qty__adjust--minus, .quantity__button--plus, .quantity__button--minus, [data-action='decrease-picker-quantity'], [data-action='increase-picker-quantity']",
  CART_REMOVE_BUTTON: "cart-remove-button",
  ORICHI_CART_POPUP_SELECTOR:
    ".t4s-tab-minicart, #CartDrawer, #cartform, .minicart, .juice-cart__content, .t4s-drawer__wrap, .cart__drawer-form, .mini-cart-content, .cart, #cart-drawer, .minicart-content, .cart-order, .cart-form, #cart__drawer, #CartDrawerForm, #js_cart_popup, .icart-content, #rebuy-cart, .ajaxcart-draw, #shopify-section-mini-cart, .cart-mini-content, .halo-cart-sidebar,.w-commerce-commercecartwrapper, .m_cart-group .cart-info,.hs-site-cart-popup-layout,form[action='/cart'],form[action='/cart'],form[action='/checkout'],#cart,.ch_customer_cart,#cartformpargo,#cart-summary,.wrapper-top-cart, #shopify-section-cart-template form, .cart-wrapper,form[action='/fr/cart'],#shopify-section-cart-template .cart-wrapper, .ajaxcart, .js-popup-cart-ajax, .cart .popup-tab, .grid__item .cart, #dropdown-cart,#slidecarthq,.cart-preview,.cart-popup-content,.mini-cart-inner-desktop,.site-cart,.cart--form,#buy-form,#CartPopup,.site-header-cart__dropdown-cart-footer-checkout, #shopify-section-gocart",
  ORICHI_DISABLE_BUTTON_APP_IF_LIMITED: ".orichi-order__shippingDetails--submit",
  ORICHI_BUTTON_DYNAMIC_CHECKOUT: ".dynamic-checkout__content"
};
