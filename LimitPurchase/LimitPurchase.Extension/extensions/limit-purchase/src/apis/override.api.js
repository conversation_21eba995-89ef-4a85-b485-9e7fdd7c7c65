export const overrideApi = async (func) => {
  let isProcessing = false;
  let timeoutId = null;

  // Debounced function to handle cart updates
  const handleCartUpdate = () => {
    if (isProcessing) return;
    isProcessing = true;

    // Clear any existing timeout
    if (timeoutId) clearTimeout(timeoutId);

    // Set a new timeout to execute the function
    timeoutId = setTimeout(async () => {
      await func();
      isProcessing = false;
    }, 100);
  };

  // Override XMLHttpRequest
  try {
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function (...args) {
      this.addEventListener("loadend", function () {
        if (
          this._url &&
          this._url.search(/cart\/add|cart\/change/) >= 0 &&
          this._method &&
          this._method.toUpperCase() !== "GET"
        ) {
          handleCartUpdate();
        }
      });
      originalOpen.apply(this, args);
    };
  } catch (error) {
    console.error("Error overriding XMLHttpRequest:", error);
  }

  // Override fetch
  try {
    const originalFetch = fetch;
    window.fetch = function (input, init) {
      const isCartUpdate =
        typeof input.search === "function" &&
        (input.search(/cart\/add|cart\/change/) >= 0 ||
          input.search("/api/2023-07/graphql.json") >= 0) &&
        input.search("ba_request") < 0;

      const isPostOrPut = init && ["POST", "PUT"].includes((init.method || "").toUpperCase());

      const response = originalFetch(input, init);

      if (isCartUpdate && isPostOrPut) {
        response.then(() => {
          handleCartUpdate();
        });
      }

      return response;
    };
  } catch (error) {
    console.error("Error overriding fetch:", error);
  }

  // Override ShopifyAPI.changeItem if it exists
  if (
    typeof ShopifyAPI !== "undefined" &&
    ShopifyAPI.changeItem &&
    !ShopifyAPI.changeItem.toString().includes("func")
  ) {
    const originalChangeItem = ShopifyAPI.changeItem;
    ShopifyAPI.changeItem = function (line, quantity, callback) {
      originalChangeItem(line, quantity, (...args) => {
        callback(...args);
        handleCartUpdate();
      });
    };
  }
};
